{"name": "chikara-frontend", "version": "0.16.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "start": "vite", "build": "vite build", "test": "vitest", "test:watch": "vitest watch", "test:visual": "playwright test --config=playwright/playwright.config.ts", "test:visual:update": "playwright test --config=playwright/playwright.config.ts --update-snapshots", "lint": "eslint --fix --ext .js,.jsx,.ts,.tsx,.json src", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"@date-fns/utc": "^2.1.0", "@formkit/auto-animate": "^0.8.2", "@headlessui/react": "^2.2.4", "@orpc/client": "^1.6.8", "@orpc/tanstack-query": "^1.6.8", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.5", "@sentry/browser": "^9.38.0", "@sentry/react": "^9.38.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@unpic/react": "^1.0.1", "@vitejs/plugin-react": "^4.6.0", "ag-grid-community": "^34.0.1", "ag-grid-react": "^34.0.1", "axios": "^1.10.0", "better-auth": "^1.2.12", "canvas-confetti": "^1.9.3", "chart.js": "^4.5.0", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "elkjs": "^0.10.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.10.0", "framer-motion": "^12.23.3", "json-with-bigint": "^3.4.4", "lucide-react": "^0.525.0", "posthog-js": "^1.257.0", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-countdown": "^2.3.6", "react-cropper": "^2.3.3", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-portal": "^4.3.0", "react-quiz-component": "^0.9.1", "react-router-dom": "^7.6.3", "react-slot-counter": "^3.3.1", "react-string-replace": "^1.1.1", "react-tooltip": "^5.29.1", "react-tracked": "^2.0.1", "reactflow": "^11.11.4", "socket.io-client": "^4.8.1", "survey-core": "^2.2.4", "survey-react-ui": "^2.2.4", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.11", "vaul": "^1.1.2", "vite": "^7.0.4", "zustand": "^5.0.6"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": ["defaults", "not dead"], "devDependencies": {"@eslint/js": "^9.31.0", "@playwright/test": "^1.54.1", "@sentry/vite-plugin": "^3.5.0", "@tailwindcss/forms": "^0.5.10", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.83.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/canvas-confetti": "^1.9.0", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-portal": "^4.0.7", "@typescript/native-preview": "^7.0.0-dev.20250712.1", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^9.31.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import-x": "^4.16.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "6.1.0-canary-97cdd5d3-20250710", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.3.0", "jsdom": "^26.1.0", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite-plugin-pwa": "^1.0.1", "vitest": "^3.2.4", "workbox-core": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-window": "^7.3.0"}}